-- Fix RLS policies for project creation
-- Date: 2025-08-02

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Contractors can create projects" ON projects;
DROP POLICY IF EXISTS "Enable insert for contractors" ON projects;

-- Create a simple policy that allows authenticated contractors to create projects
CREATE POLICY "Allow contractors to create projects" ON projects
    FOR INSERT
    TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_role = 'contractor'
            AND contractor_id IS NOT NULL
            AND deleted_at IS NULL
        )
        AND contractor_id = (
            SELECT contractor_id FROM users WHERE id = auth.uid()
        )
    );